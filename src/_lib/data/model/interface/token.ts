import { ApiModelBaseBuilder } from "@/_core/lib/data/interface/builder";
import { ENTITIES } from "../../../config/entities";
import { getEntityKey, getActionKeys } from "@/_lib/utils/entities";
import { tokenSchemas } from "../schema/token";

const { token } = ENTITIES;

// Nombre de la entidad
export const NAME = getEntityKey(token) as string;

// Acciones disponibles en la entidad
export const ACTION_NAMES = getActionKeys(token) as (keyof typeof tokenSchemas)[];

// Modelos generados
export const tokenModel = new ApiModelBaseBuilder(ACTION_NAMES[0])
  .setBody(tokenSchemas.login.body)
  .setResponse(tokenSchemas.login.response)
  .build();

export const refreshTokenModel = new ApiModelBaseBuilder(ACTION_NAMES[1])
  .setBody(tokenSchemas.refresh.body)
  .setResponse(tokenSchemas.refresh.response)
  .build();

export const recoveryTokenModel = new ApiModelBaseBuilder(ACTION_NAMES[3])
  .setBody(tokenSchemas.recovery.body)
  .setResponse(tokenSchemas.recovery.response)
  .build();