'server-only';

import { cookies } from "next/headers";

import { validateApiInput } from "@/_core/lib/service/validationService";
import { BaseHttpRequestBuilder } from "@/_core/lib/data/action/builder";
import { AsociadosInterface } from "@/_lib/data/model/interface/asociados";

import { ENTITIES } from "@/_lib/config/entities";

const { asociados } = ENTITIES;

export async function submitAsociadosCreate(body: FormData) {
  "use server";

  const {
    create: { meta },
  } = asociados;
  const ObjectBody = Object.fromEntries(body);

  const builder = new BaseHttpRequestBuilder(meta.url, meta.method)
    .setBody(ObjectBody)
    .addBeforeBuild(() => {
      const result = validateApiInput(
        AsociadosInterface.createAsociadosModel,
        "body",
        ObjectBody
      );
      builder.setBody(result);
    })
    .addAfterExecute((response) => {
      const result = validateApiInput(
        AsociadosInterface.createAsociadosModel,
        "response",
        response.data
      );
      response.data = result;
    });

  const response = await builder.run();
  return response.data;
}
