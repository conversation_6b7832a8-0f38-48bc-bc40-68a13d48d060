import { z } from "zod";
import { ENTITIES } from "@/_lib/config/entities";

export const ENTITY = ENTITIES.asociados;

/*
collection: {
  id: 0,
  nombre: "string",
  apellido: "string",
  tipoAsociado: {
    id: 0,
    nombre: "string",
    key: "string",
  },
  domicilio: {
    calle: "string",
    numero: "string",
    piso: "string",
    depto: "string",
    telefonoCodigoArea: "string",
    telefonoNumero: "string",
    email: "string",
    descripcion: "string",
    localidad: {
      id: 0,
      nombre: "string",
      codigoPostal: "string",
      partido: {
        id: 0,
        nombre: "string",
        provincia: {
          id: 0,
          nombre: "string",
        },
      },
    },
    fechaDesde: "2025-08-02T17:22:03.739Z",
    activo: true,
    id: 0,
  },
  matriculas: [
    {
        id: 0,
        numero: 0,
        fechaVencimiento: "2025-08-02T17:22:03.739Z",
        fechaAlta: "2025-08-02T17:22:03.739Z",
        formacion: {
            id: 0,
            fechaEmisionTitulo: "2025-08-02T17:22:03.739Z",
            categoria: {
            id: 0,
            nombre: "string",
            },
        },
        estadoActual: {
            id: 0,
            fechaDesde: "2025-08-02T17:22:03.739Z",
            activo: true,
            referencia: "string",
            estado: {
            id: 0,
            nombre: "string",
            key: "string",
            },
            motivo: {
            id: 0,
            nombre: "string",
            key: "string",
            },
            observacion: "string",
        },
        },
    ],
    usuario: {
        id: 0,
        username: "string",
        email: "string",
        activo: true,
        asociado: "string",
    },
    };

    index: {
    "id": 0,
    "nombre": "string",
    "apellido": "string",
    "fechaNacimiento": "2025-08-02T17:35:15.295Z",
    "dni": 0,
    "acreditacion": true,
    "smp": true,
    "smpVencimiento": "2025-08-02T17:35:15.295Z",
    "sssaludNumero": "string",
    "sssaludVencimiento": "2025-08-02T17:35:15.295Z",
    "cpppn": "string",
    "otros": "string",
    "tipoAsociado": {
        "id": 0,
        "nombre": "string",
        "key": "string"
    },
    "usuario": {
        "id": 0,
        "username": "string",
        "email": "string",
        "activo": true,
        "asociado": {
        "id": 0,
        "nombre": "string",
        "apellido": "string",
        "tipoAsociado": {
            "id": 0,
            "nombre": "string",
            "key": "string"
        },
        "domicilio": {
            "calle": "string",
            "numero": "string",
            "piso": "string",
            "depto": "string",
            "telefonoCodigoArea": "string",
            "telefonoNumero": "string",
            "email": "string",
            "descripcion": "string",
            "localidad": {
            "id": 0,
            "nombre": "string",
            "codigoPostal": "string",
            "partido": {
                "id": 0,
                "nombre": "string",
                "provincia": {
                "id": 0,
                "nombre": "string"
                }
            }
            },
            "fechaDesde": "2025-08-02T17:35:15.295Z",
            "activo": true,
            "id": 0
        },
        "matriculas": [
            {
            "id": 0,
            "numero": 0,
            "fechaVencimiento": "2025-08-02T17:35:15.295Z",
            "fechaAlta": "2025-08-02T17:35:15.295Z",
            "formacion": {
                "id": 0,
                "fechaEmisionTitulo": "2025-08-02T17:35:15.295Z",
                "categoria": {
                "id": 0,
                "nombre": "string"
                }
            },
            "estadoActual": {
                "id": 0,
                "fechaDesde": "2025-08-02T17:35:15.295Z",
                "activo": true,
                "referencia": "string",
                "estado": {
                "id": 0,
                "nombre": "string",
                "key": "string"
                },
                "motivo": {
                "id": 0,
                "nombre": "string",
                "key": "string"
                },
                "observacion": "string"
            }
            }
        ],
        "usuario": {
            "id": 0,
            "username": "string",
            "email": "string",
            "activo": true,
            "asociado": "string"
        }
        }
    },
    "matriculas": [
        {
        "id": 0,
        "numero": 0,
        "fechaVencimiento": "2025-08-02T17:35:15.295Z",
        "fechaAlta": "2025-08-02T17:35:15.295Z",
        "formacion": {
            "id": 0,
            "fechaEmisionTitulo": "2025-08-02T17:35:15.295Z",
            "categoria": {
            "id": 0,
            "nombre": "string"
            }
        },
        "estadoActual": {
            "id": 0,
            "fechaDesde": "2025-08-02T17:35:15.295Z",
            "activo": true,
            "referencia": "string",
            "estado": {
            "id": 0,
            "nombre": "string",
            "key": "string"
            },
            "motivo": {
            "id": 0,
            "nombre": "string",
            "key": "string"
            },
            "observacion": "string"
        }
        }
    ],
    "domicilios": [
        {
        "calle": "string",
        "numero": "string",
        "piso": "string",
        "depto": "string",
        "telefonoCodigoArea": "string",
        "telefonoNumero": "string",
        "email": "string",
        "descripcion": "string",
        "localidad": {
            "id": 0,
            "nombre": "string",
            "codigoPostal": "string",
            "partido": {
            "id": 0,
            "nombre": "string",
            "provincia": {
                "id": 0,
                "nombre": "string"
            }
            }
        },
        "fechaDesde": "2025-08-02T17:35:15.295Z",
        "activo": true,
        "id": 0
        }
    ]
    }

*/