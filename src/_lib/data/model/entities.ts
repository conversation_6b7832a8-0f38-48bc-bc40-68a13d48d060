import { Method } from "axios";

import { LoadStaticDataError } from "@/_core/lib/context/error/";

import { TOKEN_CONFIG } from "./config/";
import { ASOCIADOS_CONFIG } from "../../config/entities/asociados";

export type EntityActionConfig = {
  description: string;
  meta: {
    url: string; // URL de la API backend para la acción
    method: Method; // Método HTTP (GET, POST, etc.)
    revalidates: {
      keys: string[]; // Claves de revalidación para Next.js
      time?: number; // Tiempo de revalidación opcional
    }
  }
};

export type EntityActions = {
  [actionName: string]: EntityActionConfig;
};


export const ENTITIES: Record<string, EntityActions> = {
  ...TOKEN_CONFIG,
  ...ASOCIADOS_CONFIG,
} as const;

/**
 * Retorna la clave (nombre) de una entidad dentro de ENTITIES.
 */
export function getEntityKey<T extends keyof typeof ENTITIES>(
  target: (typeof ENTITIES)[T]
): T | undefined {
  return (Object.entries(ENTITIES) as [T, unknown][]).find(
    ([, value]) => value === target
  )?.[0];
}

/**
 * Retorna los nombres de acciones definidas dentro de una entidad.
 */
export function getActionKeys<T extends keyof typeof ENTITIES>(
  entity: (typeof ENTITIES)[T]
): (keyof (typeof ENTITIES)[T])[] {
  return Object.keys(entity) as (keyof (typeof ENTITIES)[T])[];
}

/**
 * Retorna la configuración (objeto con url, method, etc) de una acción específica dentro de una entidad.
 */
export function getEntityConfig<
  E extends keyof typeof ENTITIES,
  A extends keyof (typeof ENTITIES)[E]
>(entityName: E, actionName: A): EntityActionConfig {
  const entity = ENTITIES[entityName];
  if (!entity) {
      throw new LoadStaticDataError(`${entityName}`);
  }

  const actions = getActionKeys(entity);
  if (!actions.includes(actionName)) {
      throw new LoadStaticDataError(`${entityName}.${actionName}`);
  }

  return entity[actionName];
}
